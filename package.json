{"name": "terp-mate", "version": "1.0.19", "description": "terp mate", "productName": "TerpMate", "author": "chinarui-na <<EMAIL>>", "private": true, "scripts": {"test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build", "build:win": "quasar build -m electron -T win32", "build:win:standard": "quasar build -m electron -T win32 --publish=never", "build:win:portable": "quasar build -m electron -T win32 --publish=never --portable", "build:win:both": "npm run build:win:standard && npm run build:win:portable"}, "dependencies": {"@electron/notarize": "^2.4.0", "@element-plus/icons-vue": "^2.1.0", "@quasar/extras": "^1.16.4", "@spotxyz/macos-audio-devices": "^1.5.0", "animate.css": "^4.1.1", "applescript": "^1.0.0", "axios": "^1.2.1", "chokidar": "^4.0.1", "crypto-js": "^4.2.0", "dmg-license": "^1.0.11", "docx-preview": "^0.3.3", "electron-log": "^5.4.1", "electron-store": "^8.0.0", "electron-updater": "^6.3.9", "element-plus": "^2.8.0", "exec-sh": "^0.4.0", "extract-zip": "^2.0.1", "file-saver": "^2.0.5", "html-to-docx": "^1.8.0", "js-cookie": "^3.0.5", "microsoft-cognitiveservices-speech-sdk": "^1.44.1", "moment": "^2.30.1", "openai": "^4.77.0", "pdfjs-dist": "^4.8.69", "pinia": "^2.0.11", "quasar": "^2.16.0", "sudo-prompt": "^9.2.1", "uuid": "^11.0.3", "vue": "^3.4.18", "vue-router": "^4.0.12", "vuex": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@electron/packager": "^18.3.2", "@quasar/app-vite": "^1.9.0", "autoprefixer": "^10.4.2", "electron": "^20.3.8", "electron-builder": "^24.3.0", "postcss": "^8.4.14"}, "engines": {"node": "^20 || ^18 || ^16", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}