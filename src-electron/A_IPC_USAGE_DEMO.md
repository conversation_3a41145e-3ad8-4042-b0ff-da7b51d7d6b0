# IPC 使用教程和示例

本文档展示如何在渲染进程中使用各种IPC通信功能，包括新增的Windows Live Caption控制功能。

## 目录
- [基础IPC使用方法](#基础ipc使用方法)
- [文本语言检测](#文本语言检测)
- [Windows Live Caption控制](#windows-live-caption控制)
- [其他IPC功能](#其他ipc功能)

## 基础IPC使用方法

### 在渲染进程中调用IPC

```javascript
// 使用 window.electronAPI 调用主进程功能
// 这需要在 electron-preload.js 中正确配置

// 异步调用示例
const result = await ipcRenderer.invoke('ipc-method-name', param1, param2)

// 监听主进程发送的消息
window.electronAPI.on('event-name', (data) => {
  console.log('收到主进程消息:', data)
})
```

## 文本语言检测

### 检测文本语言

```javascript
// 基础语言检测
async function detectLanguage() {
  try {
    const result = await ipcRenderer.invoke('detectTextLanguage', '这是一段中文文本')
    if (result.success) {
      console.log('检测结果:', result.data)
      // result.data 格式: { dominant: { language: 'zh', ratio: 0.95 }, ratios: { zh: 0.95, en: 0.05 } }
    } else {
      console.error('检测失败:', result.error)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}

// 指定语言列表检测
async function detectLanguageWithLangs() {
  try {
    const result = await ipcRenderer.invoke('detectTextLanguageWithLangs', 
      'Hello 你好 こんにちは', ['en', 'zh', 'ja'])
    if (result.success) {
      console.log('多语言检测结果:', result.data)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}

// 简单检测（兼容旧版本）
async function detectLanguageSimple() {
  try {
    const result = await ipcRenderer.invoke('detectTextLanguageSimple', 'Hello world')
    if (result.success) {
      console.log('简单检测结果:', result.data)
      // result.data 格式: { language: 'en', confidence: 0.95 }
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}

// 获取支持信息
async function getLanguageDetectorSupport() {
  try {
    const result = await ipcRenderer.invoke('getLanguageDetectorSupportInfo')
    if (result.success) {
      console.log('支持信息:', result.data)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}
```

## Windows Live Caption控制

### 基础控制功能

```javascript
// 启动 Live Caption
async function startLiveCaption() {
  try {
    const result = await ipcRenderer.invoke('winLiveCaption-start')
    if (result.success) {
      console.log('Live Caption 启动成功:', result.data)
    } else {
      console.error('启动失败:', result.error)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}

// 停止 Live Caption
async function stopLiveCaption() {
  try {
    const result = await ipcRenderer.invoke('winLiveCaption-stop')
    if (result.success) {
      console.log('Live Caption 停止成功:', result.data)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}

// 隐藏窗口
async function hideLiveCaption() {
  try {
    const result = await ipcRenderer.invoke('winLiveCaption-hide')
    if (result.success) {
      console.log('Live Caption 窗口已隐藏:', result.data)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}

// 显示窗口
async function showLiveCaption() {
  try {
    const result = await ipcRenderer.invoke('winLiveCaption-show')
    if (result.success) {
      console.log('Live Caption 窗口已显示:', result.data)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}
```

### 字幕内容获取

```javascript
// 获取当前字幕内容
async function getCurrentCaption() {
  try {
    const result = await ipcRenderer.invoke('winLiveCaption-getCurrentCaption')
    if (result.success) {
      console.log('当前字幕:', result.data.caption)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}

// 开始实时监控字幕变化
async function startWatchingCaption() {
  try {
    // 首先设置监听字幕更新事件
    window.electronAPI.on('winLiveCaption-captionUpdate', (captionData) => {
      console.log('字幕更新:', captionData.caption)
      // 在这里处理字幕更新，比如显示在界面上
      updateCaptionDisplay(captionData.caption)
    })

    // 开始监控
    const result = await ipcRenderer.invoke('winLiveCaption-startWatchCaption')
    if (result.success) {
      console.log('字幕监控已启动:', result.data)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}

// 停止实时监控字幕变化
async function stopWatchingCaption() {
  try {
    const result = await ipcRenderer.invoke('winLiveCaption-stopWatchCaption')
    if (result.success) {
      console.log('字幕监控已停止:', result.data)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}

// 字幕显示更新函数示例
function updateCaptionDisplay(caption) {
  const captionElement = document.getElementById('caption-display')
  if (captionElement) {
    captionElement.textContent = caption
  }
}
```

### 语言设置

```javascript
// 设置识别语言
async function setRecognitionLanguage() {
  try {
    const result = await ipcRenderer.invoke('winLiveCaption-setLanguage', '简体中文')
    if (result.success) {
      console.log('语言设置成功:', result.data)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}

// 获取可用语言列表
async function getLanguageList() {
  try {
    // 基础获取
    const result = await ipcRenderer.invoke('winLiveCaption-getLanguageList')
    if (result.success) {
      console.log('可用语言:', result.data.languages)
      if (result.data.translations) {
        console.log('语言翻译:', result.data.translations)
      }
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}

// 获取语言列表（带选项）
async function getLanguageListWithOptions() {
  try {
    const options = {
      fast: true,           // 使用快速滚动
      noTranslate: false,   // 需要翻译
      apiKey: 'your-api-key' // 可选的API密钥
    }
    
    const result = await ipcRenderer.invoke('winLiveCaption-getLanguageList', options)
    if (result.success) {
      console.log('语言列表:', result.data)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}
```

### 音频设置

```javascript
// 启用麦克风音频
async function enableMicAudio() {
  try {
    const result = await ipcRenderer.invoke('winLiveCaption-enableMicAudio')
    if (result.success) {
      console.log('麦克风音频已启用:', result.data)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}

// 禁用麦克风音频
async function disableMicAudio() {
  try {
    const result = await ipcRenderer.invoke('winLiveCaption-disableMicAudio')
    if (result.success) {
      console.log('麦克风音频已禁用:', result.data)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}
```

### 状态查询

```javascript
// 获取支持信息
async function getLiveCaptionSupportInfo() {
  try {
    const result = await ipcRenderer.invoke('winLiveCaption-getSupportInfo')
    if (result.success) {
      console.log('平台支持信息:', result.data)
      console.log('是否支持:', result.data.isSupported)
      console.log('当前平台:', result.data.currentPlatform)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}

// 检查是否正在监控字幕
async function checkWatchingStatus() {
  try {
    const result = await ipcRenderer.invoke('winLiveCaption-isWatching')
    if (result.success) {
      console.log('是否正在监控:', result.data.isWatching)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}

// 获取进程信息
async function getProcessInfo() {
  try {
    const result = await ipcRenderer.invoke('winLiveCaption-getProcessInfo')
    if (result.success) {
      console.log('活跃进程数量:', result.data.activeProcessCount)
      console.log('活跃进程ID:', result.data.activeProcessIds)
      console.log('是否正在监控:', result.data.isWatching)
    }
  } catch (error) {
    console.error('调用失败:', error)
  }
}
```

## 完整使用示例

### Vue组件中的完整示例

```vue
<template>
  <div class="live-caption-controller">
    <h2>Windows Live Caption 控制器</h2>

    <!-- 基础控制 -->
    <div class="control-section">
      <h3>基础控制</h3>
      <button @click="startLiveCaption" :disabled="!isSupported">启动 Live Caption</button>
      <button @click="stopLiveCaption" :disabled="!isSupported">停止 Live Caption</button>
      <button @click="hideLiveCaption" :disabled="!isSupported">隐藏窗口</button>
      <button @click="showLiveCaption" :disabled="!isSupported">显示窗口</button>
    </div>

    <!-- 字幕监控 -->
    <div class="caption-section">
      <h3>字幕监控</h3>
      <button @click="toggleWatching" :disabled="!isSupported">
        {{ isWatching ? '停止监控' : '开始监控' }}
      </button>
      <button @click="getCurrentCaption" :disabled="!isSupported">获取当前字幕</button>

      <div class="caption-display">
        <h4>实时字幕:</h4>
        <p>{{ currentCaption || '暂无字幕' }}</p>
      </div>
    </div>

    <!-- 语言设置 -->
    <div class="language-section">
      <h3>语言设置</h3>
      <select v-model="selectedLanguage" @change="setLanguage">
        <option value="">选择语言</option>
        <option v-for="lang in availableLanguages" :key="lang" :value="lang">
          {{ lang }}
        </option>
      </select>
      <button @click="refreshLanguageList" :disabled="!isSupported">刷新语言列表</button>
    </div>

    <!-- 音频设置 -->
    <div class="audio-section">
      <h3>音频设置</h3>
      <button @click="enableMicAudio" :disabled="!isSupported">启用麦克风音频</button>
      <button @click="disableMicAudio" :disabled="!isSupported">禁用麦克风音频</button>
    </div>

    <!-- 状态信息 -->
    <div class="status-section">
      <h3>状态信息</h3>
      <p>平台支持: {{ isSupported ? '是' : '否' }}</p>
      <p>正在监控: {{ isWatching ? '是' : '否' }}</p>
      <p>活跃进程数: {{ processInfo.activeProcessCount }}</p>
    </div>

    <!-- 日志 -->
    <div class="log-section">
      <h3>操作日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LiveCaptionController',
  data() {
    return {
      isSupported: false,
      isWatching: false,
      currentCaption: '',
      selectedLanguage: '',
      availableLanguages: [],
      processInfo: {
        activeProcessCount: 0,
        activeProcessIds: []
      },
      logs: []
    }
  },

  async mounted() {
    await this.checkSupport()
    await this.refreshLanguageList()
    await this.updateProcessInfo()
    this.setupCaptionListener()
  },

  methods: {
    // 添加日志
    addLog(message) {
      this.logs.unshift({
        time: new Date().toLocaleTimeString(),
        message
      })
      if (this.logs.length > 50) {
        this.logs.pop()
      }
    },

    // 检查平台支持
    async checkSupport() {
      try {
        const result = await ipcRenderer.invoke('winLiveCaption-getSupportInfo')
        if (result.success) {
          this.isSupported = result.data.isSupported
          this.addLog(`平台支持检查: ${this.isSupported ? '支持' : '不支持'}`)
        }
      } catch (error) {
        this.addLog(`检查支持失败: ${error.message}`)
      }
    },

    // 启动 Live Caption
    async startLiveCaption() {
      try {
        const result = await ipcRenderer.invoke('winLiveCaption-start')
        if (result.success) {
          this.addLog('Live Caption 启动成功')
        } else {
          this.addLog(`启动失败: ${result.error}`)
        }
      } catch (error) {
        this.addLog(`启动失败: ${error.message}`)
      }
    },

    // 停止 Live Caption
    async stopLiveCaption() {
      try {
        const result = await ipcRenderer.invoke('winLiveCaption-stop')
        if (result.success) {
          this.addLog('Live Caption 停止成功')
        } else {
          this.addLog(`停止失败: ${result.error}`)
        }
      } catch (error) {
        this.addLog(`停止失败: ${error.message}`)
      }
    },

    // 隐藏窗口
    async hideLiveCaption() {
      try {
        const result = await ipcRenderer.invoke('winLiveCaption-hide')
        if (result.success) {
          this.addLog('Live Caption 窗口已隐藏')
        } else {
          this.addLog(`隐藏失败: ${result.error}`)
        }
      } catch (error) {
        this.addLog(`隐藏失败: ${error.message}`)
      }
    },

    // 显示窗口
    async showLiveCaption() {
      try {
        const result = await ipcRenderer.invoke('winLiveCaption-show')
        if (result.success) {
          this.addLog('Live Caption 窗口已显示')
        } else {
          this.addLog(`显示失败: ${result.error}`)
        }
      } catch (error) {
        this.addLog(`显示失败: ${error.message}`)
      }
    },

    // 切换监控状态
    async toggleWatching() {
      if (this.isWatching) {
        await this.stopWatching()
      } else {
        await this.startWatching()
      }
    },

    // 开始监控字幕
    async startWatching() {
      try {
        const result = await ipcRenderer.invoke('winLiveCaption-startWatchCaption')
        if (result.success) {
          this.isWatching = true
          this.addLog('字幕监控已启动')
        } else {
          this.addLog(`启动监控失败: ${result.error}`)
        }
      } catch (error) {
        this.addLog(`启动监控失败: ${error.message}`)
      }
    },

    // 停止监控字幕
    async stopWatching() {
      try {
        const result = await ipcRenderer.invoke('winLiveCaption-stopWatchCaption')
        if (result.success) {
          this.isWatching = false
          this.addLog('字幕监控已停止')
        } else {
          this.addLog(`停止监控失败: ${result.error}`)
        }
      } catch (error) {
        this.addLog(`停止监控失败: ${error.message}`)
      }
    },

    // 获取当前字幕
    async getCurrentCaption() {
      try {
        const result = await ipcRenderer.invoke('winLiveCaption-getCurrentCaption')
        if (result.success) {
          this.currentCaption = result.data.caption || '暂无字幕'
          this.addLog(`获取字幕: ${this.currentCaption}`)
        } else {
          this.addLog(`获取字幕失败: ${result.error}`)
        }
      } catch (error) {
        this.addLog(`获取字幕失败: ${error.message}`)
      }
    },

    // 设置字幕监听器
    setupCaptionListener() {
      window.electronAPI.on('winLiveCaption-captionUpdate', (captionData) => {
        this.currentCaption = captionData.caption
        this.addLog(`字幕更新: ${captionData.caption}`)
      })
    },

    // 设置语言
    async setLanguage() {
      if (!this.selectedLanguage) return

      try {
        const result = await ipcRenderer.invoke('winLiveCaption-setLanguage', this.selectedLanguage)
        if (result.success) {
          this.addLog(`语言设置成功: ${this.selectedLanguage}`)
        } else {
          this.addLog(`语言设置失败: ${result.error}`)
        }
      } catch (error) {
        this.addLog(`语言设置失败: ${error.message}`)
      }
    },

    // 刷新语言列表
    async refreshLanguageList() {
      try {
        const result = await ipcRenderer.invoke('winLiveCaption-getLanguageList')
        if (result.success) {
          this.availableLanguages = result.data.languages || []
          this.addLog(`获取到 ${this.availableLanguages.length} 种语言`)
        } else {
          this.addLog(`获取语言列表失败: ${result.error}`)
        }
      } catch (error) {
        this.addLog(`获取语言列表失败: ${error.message}`)
      }
    },

    // 启用麦克风音频
    async enableMicAudio() {
      try {
        const result = await ipcRenderer.invoke('winLiveCaption-enableMicAudio')
        if (result.success) {
          this.addLog('麦克风音频已启用')
        } else {
          this.addLog(`启用麦克风音频失败: ${result.error}`)
        }
      } catch (error) {
        this.addLog(`启用麦克风音频失败: ${error.message}`)
      }
    },

    // 禁用麦克风音频
    async disableMicAudio() {
      try {
        const result = await ipcRenderer.invoke('winLiveCaption-disableMicAudio')
        if (result.success) {
          this.addLog('麦克风音频已禁用')
        } else {
          this.addLog(`禁用麦克风音频失败: ${result.error}`)
        }
      } catch (error) {
        this.addLog(`禁用麦克风音频失败: ${error.message}`)
      }
    },

    // 更新进程信息
    async updateProcessInfo() {
      try {
        const result = await ipcRenderer.invoke('winLiveCaption-getProcessInfo')
        if (result.success) {
          this.processInfo = result.data
          this.isWatching = result.data.isWatching
        }
      } catch (error) {
        this.addLog(`获取进程信息失败: ${error.message}`)
      }
    }
  }
}
</script>

<style scoped>
.live-caption-controller {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.control-section, .caption-section, .language-section,
.audio-section, .status-section, .log-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.control-section h3, .caption-section h3, .language-section h3,
.audio-section h3, .status-section h3, .log-section h3 {
  margin-top: 0;
}

button {
  margin: 5px;
  padding: 8px 16px;
  border: none;
  border-radius: 3px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  background-color: #0056b3;
}

select {
  margin: 5px;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 3px;
}

.caption-display {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 3px;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 3px;
}

.log-item {
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 10px;
}

.log-message {
  color: #333;
}
</style>
```

## 其他IPC功能

### 系统音频控制

```javascript
// 获取系统扬声器音量
async function getSystemSpeakerVolume() {
  try {
    const volume = await ipcRenderer.invoke('getSystemSpeakerVolume')
    console.log('系统扬声器音量:', volume)
  } catch (error) {
    console.error('获取音量失败:', error)
  }
}

// 监听音量变化
window.electronAPI.on('changeLoudSpeakerVolume', (volumeData) => {
  console.log('扬声器音量变化:', volumeData.volume)
})
```

### ASR状态控制

```javascript
// 停止ASR
async function stopAsr() {
  try {
    const result = await ipcRenderer.invoke('stopAsr', { reason: 'user_request' })
    console.log('ASR停止结果:', result)
  } catch (error) {
    console.error('停止ASR失败:', error)
  }
}

// ASR状态检测
async function checkAsrStatus() {
  try {
    const result = await ipcRenderer.invoke('asrStatus', { asrStatus: false })
    console.log('ASR状态检测完成')
  } catch (error) {
    console.error('ASR状态检测失败:', error)
  }
}
```

### 存储操作

```javascript
// 获取密码
async function getPassword() {
  try {
    const password = await ipcRenderer.invoke('getPassword')
    console.log('获取到密码:', password ? '***' : '无密码')
  } catch (error) {
    console.error('获取密码失败:', error)
  }
}

// 保存密码
function savePassword(password) {
  window.electronAPI.send('savePassword', password)
}

// 获取双语阅读器语言信息
async function getBilingualReaderLangInfo() {
  try {
    const langInfo = await ipcRenderer.invoke('getBilingualReaderLangInfo')
    console.log('双语阅读器语言信息:', langInfo)
  } catch (error) {
    console.error('获取语言信息失败:', error)
  }
}
```

## 注意事项

1. **错误处理**: 所有IPC调用都应该包含适当的错误处理
2. **平台兼容性**: Windows Live Caption功能仅在Windows平台可用
3. **资源清理**: 在组件销毁时记得停止监控进程
4. **权限要求**: 某些功能可能需要管理员权限
5. **性能考虑**: 实时监控会持续占用系统资源，不使用时应及时停止

## 调试技巧

1. 在开发者工具中查看控制台输出
2. 检查主进程日志了解详细错误信息
3. 使用 `getProcessInfo` 方法监控进程状态
4. 在生产环境中关闭详细日志输出
