; NSIS installer script for TerpMate
; This script allows multiple installation types to coexist

!include "MUI2.nsh"
!include "FileFunc.nsh"

; Define installation types
!define INSTALL_TYPE_STANDARD "Standard"
!define INSTALL_TYPE_PORTABLE "Portable"

; Registry keys for different installation types
!define REG_UNINSTALL_STANDARD "Software\Microsoft\Windows\CurrentVersion\Uninstall\TerpMate-Standard"
!define REG_UNINSTALL_PORTABLE "Software\Microsoft\Windows\CurrentVersion\Uninstall\TerpMate-Portable"

; Custom installation directory based on type
Function .onInit
    ; Check if this is a portable installation
    ${GetParameters} $R0
    ${GetOptions} $R0 "/PORTABLE" $R1
    IfErrors not_portable
        ; Portable installation
        StrCpy $INSTDIR "$EXEDIR\TerpMate-Portable"
        WriteRegStr HKCU "${REG_UNINSTALL_PORTABLE}" "InstallLocation" "$INSTDIR"
        WriteRegStr HKCU "${REG_UNINSTALL_PORTABLE}" "DisplayName" "TerpMate (Portable)"
        WriteRegStr HKCU "${REG_UNINSTALL_PORTABLE}" "DisplayVersion" "${VERSION}"
        WriteRegStr HKCU "${REG_UNINSTALL_PORTABLE}" "Publisher" "GreenTerp"
        WriteRegStr HKCU "${REG_UNINSTALL_PORTABLE}" "UninstallString" "$INSTDIR\Uninstall-Portable.exe"
        Goto init_done
    not_portable:
        ; Standard installation
        StrCpy $INSTDIR "$PROGRAMFILES64\TerpMate-Standard"
        WriteRegStr HKLM "${REG_UNINSTALL_STANDARD}" "InstallLocation" "$INSTDIR"
        WriteRegStr HKLM "${REG_UNINSTALL_STANDARD}" "DisplayName" "TerpMate (Standard)"
        WriteRegStr HKLM "${REG_UNINSTALL_STANDARD}" "DisplayVersion" "${VERSION}"
        WriteRegStr HKLM "${REG_UNINSTALL_STANDARD}" "Publisher" "GreenTerp"
        WriteRegStr HKLM "${REG_UNINSTALL_STANDARD}" "UninstallString" "$INSTDIR\Uninstall-Standard.exe"
    init_done:
FunctionEnd

; Custom uninstaller for standard installation
Section "Uninstall-Standard"
    ; Remove files
    RMDir /r "$INSTDIR"
    
    ; Remove registry entries
    DeleteRegKey HKLM "${REG_UNINSTALL_STANDARD}"
    
    ; Remove shortcuts
    Delete "$DESKTOP\TerpMate (Standard).lnk"
    Delete "$SMPROGRAMS\GreenTerp\TerpMate (Standard).lnk"
    RMDir "$SMPROGRAMS\GreenTerp"
SectionEnd

; Custom uninstaller for portable installation
Section "Uninstall-Portable"
    ; Remove files
    RMDir /r "$INSTDIR"
    
    ; Remove registry entries
    DeleteRegKey HKCU "${REG_UNINSTALL_PORTABLE}"
    
    ; Remove shortcuts
    Delete "$DESKTOP\TerpMate (Portable).lnk"
SectionEnd

; Custom shortcuts creation
Function CreateShortcuts
    ${GetParameters} $R0
    ${GetOptions} $R0 "/PORTABLE" $R1
    IfErrors create_standard_shortcuts
        ; Create portable shortcuts
        CreateShortCut "$DESKTOP\TerpMate (Portable).lnk" "$INSTDIR\${PRODUCT_FILENAME}.exe"
        Goto shortcuts_done
    create_standard_shortcuts:
        ; Create standard shortcuts
        CreateDirectory "$SMPROGRAMS\GreenTerp"
        CreateShortCut "$SMPROGRAMS\GreenTerp\TerpMate (Standard).lnk" "$INSTDIR\${PRODUCT_FILENAME}.exe"
        CreateShortCut "$DESKTOP\TerpMate (Standard).lnk" "$INSTDIR\${PRODUCT_FILENAME}.exe"
    shortcuts_done:
FunctionEnd

; Check for existing installations
Function CheckExistingInstallations
    ; Check for standard installation
    ReadRegStr $R0 HKLM "${REG_UNINSTALL_STANDARD}" "InstallLocation"
    IfErrors check_portable
        MessageBox MB_YESNO "检测到已安装的标准版本。是否要继续安装？" IDYES check_portable
        Abort
    check_portable:
        ; Check for portable installation
        ReadRegStr $R0 HKCU "${REG_UNINSTALL_PORTABLE}" "InstallLocation"
        IfErrors installation_ok
        MessageBox MB_YESNO "检测到已安装的便携版本。是否要继续安装？" IDYES installation_ok
        Abort
    installation_ok:
FunctionEnd

; Main installation section
Section "MainSection" SEC01
    Call CheckExistingInstallations
    
    ; Set output path
    SetOutPath "$INSTDIR"
    
    ; Install files (this will be handled by electron-builder)
    ; The actual file copying is done by electron-builder
    
    ; Create shortcuts
    Call CreateShortcuts
    
    ; Write uninstaller
    ${GetParameters} $R0
    ${GetOptions} $R0 "/PORTABLE" $R1
    IfErrors write_standard_uninstaller
        WriteUninstaller "$INSTDIR\Uninstall-Portable.exe"
        Goto uninstaller_done
    write_standard_uninstaller:
        WriteUninstaller "$INSTDIR\Uninstall-Standard.exe"
    uninstaller_done:
SectionEnd
