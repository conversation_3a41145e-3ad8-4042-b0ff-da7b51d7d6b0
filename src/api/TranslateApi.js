import request from "@/utils/request";
import {v4} from 'uuid'
let useGT = false
export function getTranslateModelList(){
    return request({
        method: 'GET',
        url: `/conf/translateModel/listAll`
    })
}

export function translateSearch(data) {
    const translateModel = data.translateApi
    if(translateModel.type == 1){
        // 使用deeplx
        return deeplxTranslateSearch(data)
        // return microsoftTranslateSearch(data)
    }else if(translateModel.type == 2){
        //微软源语言不支持auto
        return microsoftTranslateSearch(data)
    }else if(translateModel.type == 3){
        return googleTranslateSearch(data)
    }else if(translateModel.type == 4){
        return googleTranslateSearchByChromeExtension(data)
    }
    return microsoftTranslateSearch(data)
}
//deeplx 翻译
function deeplxTranslateSearch(data) {
    const translateModel = data.translateApi
    if(process.env.NODE_ENV==='development'){
        return request({
            method: 'post',
            url: '/translate',
            data: {
                "text": data.text,
                "source_lang": data.source_lang.split('-')[0],
                "target_lang": data.target_lang.split('-')[0]
            }
        })
    }else{
        return request({
            method: 'post',
            url: translateModel.deeplxUrl,
            data: {
                "text": data.text,
                "source_lang": data.source_lang.split('-')[0],
                "target_lang": data.target_lang.split('-')[0]
            }
        })
    }
}

//微软翻译
export async function microsoftTranslateSearch(req) {
    const translateModel = req.translateApi
    if(useGT){
        translateModel.microsoftKey = '3ppwpHjYgB3Wja2mwJar7fUKp73PdYRri7vuIJOXmGFC716rzm7nJQQJ99BGAC1i4TkXJ3w3AAAbACOG1nTl'
    }
    const params = {
        'api-version': '3.0',
        'from': req.source_lang.split('-')[0],
        'to': req.target_lang.split('-')[0]
    }
    let requestUrl = '/microsoftTranslate';
    if(process.env.NODE_ENV !=='development'){
        requestUrl = 'https://api.cognitive.microsofttranslator.com/translate'
    }
    let reqHeaders = {
        'Ocp-Apim-Subscription-Key': translateModel.microsoftKey,
        'Content-type': 'application/json',
        'X-ClientTraceId': v4().toString()
    };
    // if (useGT) {
        reqHeaders['Ocp-Apim-Subscription-Region'] = 'centralus';
    // }
    let resp = null
    try {
        resp = await request({
            method: 'POST',
            url: requestUrl,
            headers: reqHeaders,
            data: [{
            'text': req.text
            }],
            params: params,
            responseType: 'json'
        });
        console.log('resp', resp);
    } catch (error) {
        //如果当前是cymo，切换成gt
        // if (translateModel.microsoftKey === 'd4471c2264d946e8969dd7c74b28e735') {
        useGT = true
        // }
    }
    // resp [{"translations":[{"text":"你好","to":"zh-Hans"}]}]
    return new Promise((resolve) => {
        resolve({
            "code": 200,
            "data": resp[0].translations[0].text
        })
    })

}

// https://clients5.google.com/translate_a/t?client=dict-chrome-ex&sl=auto&tl=zh-CN&q=hello
export async function googleTranslateSearch(req) {
    const translateModel = req.translateApi
    const params = {
        'client': 'dict-chrome-ex',
        'sl': 'auto',
        'tl': req.target_lang,
        'q': req.text
    }
    let requestUrl = '/googleTranslate'
    if(process.env.NODE_ENV !=='development'){
        requestUrl = translateModel.googleNokeyUrl
    }
    console.log('requestUrl ',requestUrl)
    let resp = await request({
        method: 'GET',
        url: requestUrl,
        params: params
    })
    console.log('resp ',resp)
    // resp [{"translations":[{"text":"你好","to":"zh-Hans"}]}]
    return new Promise((resolve) => {
        resolve({
            "code": 200,
            "data": resp[0][0]
        })
    })

}

export async function googleTranslateSearchByChromeExtension(req) {
    const translateModel = req.translateApi
    const params = {
        'language': req.target_lang,
        'key': translateModel.googleExtensionKey,
        'strategy': 2,
        'term': req.text
    }
    let requestUrl = '/chromeExtensionTranslate'
    if(process.env.NODE_ENV !=='development'){
        requestUrl = translateModel.googleExtensionUrl
    }
    console.log('requestUrl ',requestUrl)
    let resp = await request({
        method: 'GET',
        url: requestUrl,
        params: params,
        headers: {
            'x-referer': translateModel.googleXREFKey
        },
    })
    console.log('resp ',resp)
    // resp [{"translations":[{"text":"你好","to":"zh-Hans"}]}]
    return new Promise((resolve) => {
        resolve({
            "code": 200,
            "data": resp.translateResponse.translateText
        })
    })

}


// export function translateSearch(data) {
//     return deeplTranslate({ text: data.text, toLang: data.target_lang.split('-')[0], fromLang: data.source_lang.split('-')[0] })
// }

