<template>
    <div>
        <el-dialog
            v-model="settingVisible"
            :before-close="beforeCloseSetting"
            title="Settings"
            center
            class="settingDialog"
        >
            <el-form label-width="150px">
                <el-form-item label="Version" v-if="asrPattern === 1">
                    {{config.version}}
                    <el-button
                        style="margin-left:20px"
                        type="primary"
                        link
                        @click="checkForUpdates"
                    >
                        Check For Updates
                    </el-button>
                </el-form-item>
                <el-form-item label="Font Size">
                    <el-input-number
                        v-model="fontSizeSetting"
                        :min="1"
                        :max="100"
                        controls-position="right"
                        @change="changeFontSize"
                    />
                </el-form-item>
                <el-form-item label="Highlight Color">
                    <el-color-picker v-model="colorSetting" :predefine="predefineColors" @change="changeColor"/>
                </el-form-item>
                <el-form-item label="Theme Color">
                    <el-switch
                        v-model="themeSetting"
                        inline-prompt
                        :active-value=true
                        active-icon="Sunny"
                        inactive-icon="Moon"
                        style="--el-switch-off-color: #2f2f2f"
                        @change="changeTheme"
                    />
                </el-form-item>
                <el-form-item label="Primary Language">
                    <div class="language-div">
                        <el-select v-model="mainLanguage" filterable @change="handleMainLanguageLocal">
                            <el-option
                                v-for="item in mainLangList"
                                :key="item.code"
                                :label="item.show"
                                :value="item.code"
                            />
                        </el-select>
                        <el-select v-model="mainAsrEngine" filterable @change="handleChangeMainAsrEngine">
                            <el-option
                                v-for="item in mainAsrEngineList"
                                :key="item.code"
                                :label="item.show"
                                :value="item.code"
                            />
                        </el-select>
                        <el-icon
                            v-if="mainAsrEngine === 4"
                            class="info-icon"
                            @click="showWinLCGuideDialog"
                            title="View Windows Live Caption Guide"
                        >
                            <InfoFilled />
                        </el-icon>
                        <div v-if="mainAsrEngine === 4 && props.isStart" class="win-lc-controls">
                            <el-button
                                size="small"
                                :type="winLcButtonType"
                                @click="toggleWinLiveCaption"
                                :title="winLcButtonTitle"
                            >
                                {{ winLcButtonText }}
                            </el-button>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="Secondary Language">
                    <div class="language-div">
                        <el-select v-model="secondLanguage" filterable @change="handleSecondLanguageLocal">
                            <el-option
                                v-for="item in secondLangList"
                                :key="item.code"
                                :label="item.show"
                                :value="item.code"
                            />
                        </el-select>
                        <el-select v-model="secondAsrEngine" filterable @change="handleChangeSecondAsrEngine">
                            <el-option
                                v-for="item in secondAsrEngineList"
                                :key="item.code"
                                :label="item.show"
                                :value="item.code"
                            />
                        </el-select>
                        <el-icon
                            v-if="secondAsrEngine === 4"
                            class="info-icon"
                            @click="showWinLCGuideDialog"
                            title="View Windows Live Caption Guide"
                        >
                            <InfoFilled />
                        </el-icon>
                        <div v-if="secondAsrEngine === 4 && props.isStart" class="win-lc-controls">
                            <el-button
                                size="small"
                                :type="winLcButtonType"
                                :loading="winLcToggleLoading"
                                @click="toggleWinLiveCaption"
                                :title="winLcButtonTitle"
                            >
                                {{ winLcButtonText }}
                            </el-button>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="Tertiary Language" v-if="macShowAuto">
                    <div class="language-div">
                        <el-select v-model="thirdLanguage" filterable @change="handleThirdlyLanguageLocal">
                            <el-option
                                v-for="item in thirdlyLangList"
                                :key="item.code"
                                :label="item.show"
                                :value="item.code"
                            />
                        </el-select>
                        <el-select v-model="thirdlyAsrEngine" filterable @change="handleChangeThirdlyAsrEngine">
                            <el-option
                                v-for="item in thirdlyAsrEngineList"
                                :key="item.code"
                                :label="item.show"
                                :value="item.code"
                            />
                        </el-select>
                        <el-switch
                            v-model="props.openThirdlyLanguageStatus"
                            inline-prompt
                            :active-value=true
                            style="--el-switch-off-color: #2f2f2f"
                            @change="changeOpenThirdlyLanguage"
                        />
                    </div>
                </el-form-item>
                <!-- <el-form-item label="Asr Pattern">
                    <el-select v-model="asrPattern" filterable @change="handleAsrPatternChangeLocal">
                        <el-option
                            v-for="item in asrPatternList"
                            :key="item.code"
                            :label="item.show"
                            :value="item.code"
                        />
                    </el-select>
                </el-form-item> -->
                <el-form-item label="Asr Model" v-if="secondAsrEngine === 1 || mainAsrEngine === 1">
                    <el-button
                        type="primary"
                        link
                        @click="showModelManager"
                    >
                        Manage Model
                    </el-button>
                </el-form-item>
            </el-form>
        </el-dialog>

        <!-- 添加模型管理对话框 -->
        <el-dialog
            v-model="modelManagerVisible"
            title="ASR Model Manager"
            width="60%"
            center
            class="model-manager-dialog"
        >
            <el-table 
                :data="modelList" 
                style="width: 100%"
                max-height="calc(80vh - 150px)"  
            >
                <el-table-column prop="language" label="Language" />
                <!-- <el-table-column prop="size" label="Size" width="120" /> -->
                <el-table-column prop="status" label="Status">
                    <template #default="scope">
                        <el-tag
                            :type="scope.row.downloaded ? 'success' : ''"
                        >
                            {{ scope.row.downloaded ? 'Downloaded' : 'Not Downloaded' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="Operations" >
                    <template #default="scope">
                        <el-button
                            v-if="!scope.row.downloading && !scope.row.downloaded"
                            type="primary"
                            size="small"
                            @click="downloadModel(scope.row)"
                        >
                            Download
                        </el-button>
                        <el-progress
                            v-if="scope.row.downloading"
                            :percentage="scope.row.progress"
                            style="width:100%"
                        />
                        <el-button
                            v-if="scope.row.downloaded"
                            type="danger"
                            size="small"
                            @click="deleteModel(scope.row)"
                        >
                            Delete
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>

        <!-- Windows Live Caption 首次使用引导对话框 -->
        <el-dialog
            v-model="showWinLCGuide"
            width="800px"
            :show-close="false"
            :header="null"
            class="win-lc-guide-dialog"
            title="Guide "
            center
        >
            <div class="win-lc-guide">
            <!-- Step 1 -->
            <h2>Step 1: Verify Windows LiveCaptions Availability</h2>
            <p>Confirm LiveCaptions is available on your system using any of these methods:</p>
            <el-divider></el-divider>
            <ul>
                <li>Toggle <strong>Live captions</strong> in the quick settings</li>
                <li>Press <kbd>Win + Ctrl + L</kbd></li>
                <li>Access via <strong>Quick settings</strong> > <strong>Accessibility</strong> > <strong>Live captions</strong></li>
                <li>Open <strong>Start</strong> > <strong>All apps</strong> > <strong>Accessibility</strong> > <strong>Live captions</strong></li>
                <li>Navigate to <strong>Settings</strong> > <strong>Accessibility</strong> > <strong>Captions</strong> and enable <strong>Live captions</strong></li>
            </ul>

            <!-- Step 2 -->
            <h2>Step 2: Configure LiveCaptions</h2>
            <p>
                When you first start, Windows LiveCaptions will ask for your consent to process voice data on your device and prompt you to download language files for on-device speech recognition.
            </p>
            <p>
                After launching Windows LiveCaptions, click the
                <strong>⚙️ gear</strong> icon to open the settings menu, then select
                <strong>Position</strong> > <strong>Overlaid on screen</strong>.
            </p>

            <!-- <el-alert
                title="VERY IMPORTANT!"
                type="warning"
                description="Otherwise, a display bug will occur on the screen after hiding Windows LiveCaptions."
                show-icon
                :closable="false"
                class="important-note"
            ></el-alert> -->

            <div class="image-section">
                <el-image
                src="src/assets/speech_recognition.png"
                fit="contain"
                lazy
                style="max-width: 100%; width: 80%;"
                />
                <p class="caption">Required speech recognition downloads</p>
            </div>

            <p>After configuration, close Windows LiveCaptions and launch TerpMate to start using it! 🎉</p>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import { onMounted, ref, watch, computed } from "vue";
import store from "@/store";
import {langListParams, sodaLangList,xunfeiLangList,thirdLangList} from "@/utils/commonParams";
import { Platform } from 'quasar'
import {ElMessage, ElMessageBox} from "element-plus";
import { InfoFilled } from '@element-plus/icons-vue';
import config from '../../../package.json'
const { ipcRenderer } = require('electron');
const settingVisible = ref(false)
const predefineColors = ref([
    '#ff4500',
    '#ff8c00',
    '#ffd700',
    '#90ee90',
    '#00ced1',
    '#1e90ff',
    '#c71585',
])
const themeSetting = ref()
const colorSetting = ref()
const fontSizeSetting = ref()
// 中文（简体）：zh-CN
// 英文：en-US
// 西班牙文：es-ES
// 法文：fr-FR
// 德文：de-DE
// 日文：ja-JP
// 俄文：ru-RU
// 葡萄牙文：pt-PT
// 阿拉伯文：ar-SA
// 韩文：ko-KR
// 意大利文：it-IT
const allLangList = ref(sodaLangList)
const mainLangList = ref(sodaLangList)
const secondLangList = ref(sodaLangList)
const thirdlyLangList = ref(thirdLangList)
const asrPatternList = ref()
const asrPattern = ref(1)
// 主要语言
const mainLanguage = ref()
// 次要语言
const secondLanguage = ref()
// 自动语言
const thirdLanguage = ref('Auto')
const openThirdlyLanguage = ref(false)
// 添加新的响应式变量
const mainAsrEngineList = ref(null)
const mainAsrEngine = ref(null)
const secondAsrEngineList = ref(null)
const secondAsrEngine = ref(null)
const thirdlyAsrEngineList = ref(null)
const thirdlyAsrEngine = ref(null)

// Windows Live Caption 引导相关
const showWinLCGuide = ref(false)

// Windows Live Caption 控制相关
const winLcToggleLoading = ref(false)

// Windows Live Caption 按钮状态计算属性
const winLcButtonText = computed(() => {
    return props.winLcIsVisible ? 'Hide' : 'Show'
})

const winLcButtonType = computed(() => {
    return props.winLcIsVisible ? 'warning' : 'primary'
})

const winLcButtonTitle = computed(() => {
    return props.winLcIsVisible ? 'Hide Windows Live Caption' : 'Show Windows Live Caption'
})

const modelManagerVisible = ref(false)
const modelList = ref([
    { id: 1, language: 'German', code: 'de-DE', downloaded: false, downloading: false, progress: 0, url:'http://oss.chinarui.cn/greenterp/asr/de-DE.zip' },
    { id: 2, language: 'English', code: 'en-US', downloaded: false, downloading: false, progress: 0, url: 'http://oss.chinarui.cn/greenterp/asr/en-US.zip' },
    { id: 3, language: 'Spanish', code: 'es-ES', downloaded: false, downloading: false, progress: 0, url: 'http://oss.chinarui.cn/greenterp/asr/es-ES.zip' },
    { id: 4, language: 'French', code: 'fr-FR', downloaded: false, downloading: false, progress: 0, url: 'http://oss.chinarui.cn/greenterp/asr/fr-FR.zip' },
    { id: 5, language: 'India', code: 'hi-IN', downloaded: false, downloading: false, progress: 0, url: 'http://oss.chinarui.cn/greenterp/asr/hi-IN.zip' },
    { id: 6, language: 'Indonesian', code: 'id-ID', downloaded: false, downloading: false, progress: 0, url: 'http://oss.chinarui.cn/greenterp/asr/id-ID.zip' },
    { id: 7, language: 'Italian', code: 'it-IT', downloaded: false, downloading: false, progress: 0, url: 'http://oss.chinarui.cn/greenterp/asr/it-IT.zip' },
    { id: 8, language: 'Japanese', code: 'ja-JP', downloaded: false, downloading: false, progress: 0, url: 'http://oss.chinarui.cn/greenterp/asr/ja-JP.zip' },
    { id: 9, language: 'Korean', code: 'ko-KR', downloaded: false, downloading: false, progress: 0, url: 'http://oss.chinarui.cn/greenterp/asr/ko-KR.zip' },
    { id: 10, language: 'Polish', code: 'pl-PL', downloaded: false, downloading: false, progress: 0, url: 'http://oss.chinarui.cn/greenterp/asr/pl-PL.zip' },
    { id: 11, language: 'Portuguese', code: 'pt-BR', downloaded: false, downloading: false, progress: 0, url: 'http://oss.chinarui.cn/greenterp/asr/pt-BR.zip' },
    { id: 12, language: 'Thai', code: 'th-TH', downloaded: false, downloading: false, progress: 0, url: 'http://oss.chinarui.cn/greenterp/asr/th-TH.zip' },
    { id: 13, language: 'Turkish', code: 'tr-TR', downloaded: false, downloading: false, progress: 0, url: 'http://oss.chinarui.cn/greenterp/asr/tr-TR.zip' },
    { id: 14, language: 'Chinese', code: 'zh-CN', downloaded: false, downloading: false, progress: 0, url: 'http://oss.chinarui.cn/greenterp/asr/zh-CN.zip' }])

const props = defineProps({
    theme: {
        type: Boolean,
    },
    color: {
        type: String,
    },
    fontSize: {
        type: Number,
    },
    openThirdlyLanguageStatus:{
        type: Boolean
    },
    // Windows Live Caption 可见性状态
    winLcIsVisible: {
        type: Boolean,
        default: false
    },
    // ASR 启动状态
    isStart: {
        type: Boolean,
        default: false
    },
    changeColor: {
        type: Function
    },
    changeTheme: {
        type: Function
    },
    changeOpenThirdlyLanguage:{
        type: Function
    },
    changeFontSize: {
        type: Function
    },
    // 已选择的语言列表
    langList: {
        type: Array
    },
    // 切换主语言
    handleMainLanguage: {
        type: Function
    },
    // 切换次要语言
    handleSecondLanguage: {
        type: Function
    },
    //切换Asr方式
    handleAsrPatternChange: {
        type: Function
    },
    //切换主ASR引擎
    handleChangeMainAsrEngineParent: {
        type: Function
    },
    //切换第二Asr引擎
    handleChangeSecondAsrEngineParent: {
        type: Function
    },
    //切换第三Asr引擎
    handleChangeThirdlyAsrEngineParent: {
        type: Function
    },
    // Windows Live Caption 可见性状态更新
    handleUpdateWinLcVisible: {
        type: Function
    }
})
onMounted(async () => {
    themeSetting.value = props.theme
    colorSetting.value = props.color
    fontSizeSetting.value = props.fontSize
    //设置语音引擎列表
    setAsrEngineList()
    checkLanguageFile()
    setTimeout(() => {
        loadAsrEngineInfo()
    }, 500);


    ipcRenderer.on('download-progress', (event, res) => {
        const targetItem = modelList.value.find(item => item.id === res.id);
        if (targetItem) {
            targetItem.progress = res.progress;
            targetItem.downloaded = false
            targetItem.downloading = true
        }
    })
    ipcRenderer.on('download-success', (event, res) => {
        // checkLanguageFile()
       const targetItem = modelList.value.find(item => item.id === res.id);
       if (targetItem) {
        targetItem.progress = 0;
        targetItem.downloaded = true
        targetItem.downloading = false
       }
    })
    
})
watch(
    () => props.langList,
    (newValue, oldValue) => {
        newValue.forEach(item => {
            if (item.main) {
                mainLanguage.value = item.code
            } else if (item.second) {
                secondLanguage.value = item.code
            }
        })
        checkChooseLanguageWithOfflineAsr()
    }
)
watch(
    () => props.theme,
    (newValue, oldValue) => {
        themeSetting.value = newValue
    }
)
watch(
    () => props.color,
    (newValue, oldValue) => {
        colorSetting.value = newValue
    }
)
watch(
    () => props.fontSize,
    (newValue, oldValue) => {
        fontSizeSetting.value = newValue
    }
)
watch(
    () => store.getters.getSettingVisible,
    (newValue, oldValue) => {
        settingVisible.value = newValue
    }
)
const loadAsrEngineInfo = ()=>{
  const mainAsrEngineInfo = localStorage.getItem('mainAsrEngine')
  const sencondAsrEngineInfo = localStorage.getItem('sencondAsrEngine')
  const thirdlyAsrEngineInfo = localStorage.getItem('thirdlyAsrEngine')
  if(mainAsrEngineInfo && sencondAsrEngineInfo){    
    mainAsrEngine.value = Number(mainAsrEngineInfo)
    checkLanguage(mainAsrEngine.value,true)
    secondAsrEngine.value = Number(sencondAsrEngineInfo)
    checkLanguage(secondAsrEngine.value,false)
  }
  if(thirdlyAsrEngineInfo){
    thirdlyAsrEngine.value = Number(thirdlyAsrEngineInfo)
  }
}
const macShowAuto = ref(false)
const privateVersion = ()=>{
    macShowAuto.value = true
    if(Platform.is.win){
        mainAsrEngineList.value = [{
            code: 1,
            show: 'Offline'
        },{
            code: 2,
            show: 'Online-1'
        } ,{
            code: 3,
            show: 'Online-ZH/EN only'
        },{
            code: 4,
            show: 'MS-Offline'
        }]
    }else{
        mainAsrEngineList.value = [{
            code: 0,
            show: 'Hybrid'
        },{
            code: 1,
            show: 'Offline'
        },{
            code: 2,
            show: 'Online-1'
        } ,{
            code: 3,
            show: 'Online-ZH/EN only'
        }]
    }
    secondAsrEngineList.value = mainAsrEngineList.value
    if(Platform.is.win){
        thirdlyAsrEngineList.value = [{
            code: 2,
            show: 'Online-1'
        } ,{
            code: 3,
            show: 'Online-ZH/EN only'
        },{
            code: 4,
            show: 'MS-Offline'
        }]
    }else{
        thirdlyAsrEngineList.value = [{
            code: 2,
            show: 'Online-1'
        } ,{
            code: 3,
            show: 'Online-ZH/EN only'
        }]
    }
}
const publicVersion = ()=>{
    macShowAuto.value = false
    if(Platform.is.win){
        mainAsrEngineList.value = [{
            code: 1,
            show: 'Offline'
        },{
            code: 4,
            show: 'MS-Offline'
        }]
    }else{
        mainAsrEngineList.value = [{
            code: 0,
            show: 'Hybrid'
        },{
            code: 1,
            show: 'Offline'
        }]
    }
    secondAsrEngineList.value = mainAsrEngineList.value
    if(Platform.is.win){
        thirdlyAsrEngineList.value = [{
            code: 4,
            show: 'MS-Offline'
        }]
    }else{
        thirdlyAsrEngineList.value = [{
            code: 2,
            show: 'Online-1'
        } ,{
            code: 3,
            show: 'Online-ZH/EN only'
        }]
    }
}
const setAsrEngineList = ()=>{
    // ,{
    //         code: 3,
    //         show: 'Online-ZH/EN only'
    //     }
    publicVersion()
    // privateVersion()
    mainAsrEngine.value = 1
    secondAsrEngine.value = 1
    thirdlyAsrEngine.value = 2
}
const checkForUpdates = ()=>{
    ipcRenderer.send('checkForUpdates')
}
const checkLanguageFile = async () =>{
    const result = await ipcRenderer.invoke('check-asr-language', JSON.stringify(modelList.value));
    console.log('check asr language ',result)
    modelList.value = result
}
const cancelSetting = () => {
    settingVisible.value = false
    store.commit('setSettingVisible', false)
}
const beforeCloseSetting = (done) => {
    cancelSetting()
    done()
}
//切换主语言
const handleMainLanguageLocal = (value) => {
    const mainLang = mainLangList.value.find(item => item.code === value)
    props.handleMainLanguage(mainLang)
}
//切换副语言
const handleSecondLanguageLocal = (value) => {
    const secondLang = secondLangList.value.find(item => item.code === value)
    props.handleSecondLanguage(secondLang)
}
//切换主ASR
const handleChangeMainAsrEngine = (value)=>{
    // 检查是否选择了Windows Live Caption (code 4)
    if (value === 4) {
        checkWinLCFirstUse()
    }
    checkLanguage(value,true)
    props.handleChangeMainAsrEngineParent(value,mainLanguage.value)
}
//切换副ASR
const handleChangeSecondAsrEngine = (value) =>{
    // 检查是否选择了Windows Live Caption (code 4)
    if (value === 4) {
        checkWinLCFirstUse()
    }
    checkLanguage(value,false)
    props.handleChangeSecondAsrEngineParent(value,secondLanguage.value)
}
//切换auto asr 引擎
const handleChangeThirdlyAsrEngine = (value) =>{
    props.handleChangeThirdlyAsrEngineParent(value)
}
//根据语言获取引擎
const getAsrEngineByLanguage = (language) => {
    console.log('language ',language)
    if(language == mainLanguage.value){
        return mainAsrEngine.value
    }
    if(language == secondLanguage.value){
        return secondAsrEngine.value
    }
    if(language == thirdLanguage.value){
        return thirdlyAsrEngine.value
    }
    
};

// 暴露子组件方法，以便父组件可以调用
defineExpose({
  getAsrEngineByLanguage
});
//检查语言
const checkLanguage = (code,mainAsr)=>{
    if(code == 1){
        mainAsr ? mainLangList.value = sodaLangList : secondLangList.value = sodaLangList
        checkChooseLanguageWithOfflineAsr()
    }else if(code == 3){
        mainAsr ? mainLangList.value = xunfeiLangList : secondLangList.value = xunfeiLangList  
        checkChooseLanguageWithOfflineAsr()
    }else{
        mainAsr ? mainLangList.value = langListParams : secondLangList.value = langListParams
    }
    
}
//切换语音识别方式 hybird or offline
const handleAsrPatternChangeLocal = (value) => {
    const asrPattern = asrPatternList.value.find(item => item.code === value)
    if(asrPattern.code == 1){
        //offline
        allLangList.value = sodaLangList
        //需要检查切换到offline时，当前选中的语言，在offline是否也有，如果没有就没法进行语音识别
        checkChooseLanguageWithOfflineAsr()
    }else{
        allLangList.value = langListParams
    }
    props.handleAsrPatternChange(asrPattern)
}
const checkChooseLanguageWithOfflineAsr = () =>{
    const hasMainLanguage = mainLangList.value.some(item => item.code === mainLanguage.value);
    const hasSecondLanguage = secondLangList.value.some(item => item.code === secondLanguage.value);
    if(!hasMainLanguage){
        mainLanguage.value = ''
        ElMessage.warning('Please Choose Primary Language')
    }
    if(!hasSecondLanguage){
        secondLanguage.value = ''
        ElMessage.warning('Please Choose Secondary Language')
    }

}
// 添加新的方法
const showModelManager = () => {
    checkLanguageFile()
    modelManagerVisible.value = true
}

const downloadModel = (model) => {
    model.downloading = true
    ipcRenderer.send('download-file', { url: model.url, filename: model.code + '.zip',id:model.id });
}

const deleteModel = async (model) => {
    ElMessageBox.confirm(
        `Are you sure to delete ${model.language} model?`,
        'Warning',
        {
            confirmButtonText: 'Delete',
            cancelButtonText: 'Cancel',
            type: 'warning',
        }
    )
    .then(async () => {
        const result = await ipcRenderer.invoke('delete-asr-language', model.code);
        model.downloaded = false
        model.downloading = false
        ElMessage({
            type: 'success',
            message: 'Delete completed',
        })
    })
    .catch(() => {
        ElMessage({
            type: 'info',
            message: 'Delete canceled',
        })
    })
}

// 使用markdown-it解析markdown
const parseMarkdown = (markdown) => {
    // 使用markdown-it渲染
    let html = md.render(markdown)

    // 修复图片路径
    html = html.replace(/src="assets\/(.*?)"/g, 'src="src/assets/$1"')

    return html
}

// 检查Windows Live Caption首次使用
const checkWinLCFirstUse = async () => {
    const hasShownGuide = localStorage.getItem('winLCGuideShown')
    if (!hasShownGuide) {
        showWinLCGuide.value = true
        localStorage.setItem('winLCGuideShown',true)
    }
}

// 手动显示Windows Live Caption引导对话框
const showWinLCGuideDialog = async () => {
    showWinLCGuide.value = true
}



// 切换Windows Live Caption显示/隐藏
const toggleWinLiveCaption = async () => {
    winLcToggleLoading.value = true

    try {
        // 根据当前状态决定执行显示还是隐藏
        const action = props.winLcIsVisible ? 'hide' : 'show'
        const result = await ipcRenderer.invoke(`winLiveCaption-${action}`)

        if (result.success) {
            console.log(`Windows Live Caption ${action} successfully`)
            // 通知父组件更新 winLcIsVisible 状态
            props.handleUpdateWinLcVisible(action === 'show')
        } else {
            console.error(`${action} Windows Live Caption failed:`, result.error)
        }
    } catch (error) {
        console.error('Toggle Windows Live Caption error:', error)
        // ElMessage.error('Toggle Windows Live Caption error: ' + error.message)
    } finally {
        winLcToggleLoading.value = false
    }
}

const percentageFormat = (percentage) => {
    return percentage === 100 ? 'Done' : `${percentage}%`
}
</script>

<style scoped>
/* 信息图标样式 */
.info-icon {
    margin-left: 1px;
    color: #07c160;
    cursor: pointer;
    font-size: 16px;
    vertical-align: middle;
    display: inline-flex;
    align-items: center;
}

.info-icon:hover {
    color: #66b1ff;
}

/* Windows Live Caption 控制按钮样式 */
.win-lc-controls {
    display: inline-flex;
    margin-left: 8px;
    align-items: center;
}

.win-lc-controls .el-button {
    padding: 4px 12px;
    font-size: 12px;
    min-width: 80px;
}

/* Windows Live Caption 引导对话框样式 */
:deep(.win-lc-guide-dialog) {
    height: 80vh;
}

:deep(.win-lc-guide-dialog .el-dialog__body) {
    max-height: calc(80vh - 120px);
    overflow-y: auto;
    padding: 20px;
}

.win-lc-guide {
    line-height: 1.6;
    color: #606266;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 黑色主题下的样式调整 */
.dark .win-lc-guide {
    color: #e4e7ed;
}

.win-lc-guide h2 {
    color: #303133;
    margin: 20px 0 12px 0;
    font-weight: 600;
    line-height: 1.3;
    font-size: 20px;
}

/* 黑色主题下的标题颜色 */
.dark .win-lc-guide h2 {
    color: #ffffff;
}

.win-lc-guide p {
    margin: 12px 0;
    line-height: 1.6;
}

/* 黑色主题下的段落颜色 */
.dark .win-lc-guide p {
    color: #e4e7ed;
}

.win-lc-guide ul {
    margin: 12px 0;
    padding-left: 24px;
}

.win-lc-guide li {
    margin-bottom: 6px;
    line-height: 1.5;
    color: #606266;
}

/* 黑色主题下的列表项颜色 */
.dark .win-lc-guide li {
    color: #e4e7ed;
}

/* 强调文本样式 */
.win-lc-guide strong {
    color: #07c160;
    font-weight: 600;
}

/* 黑色主题下的强调文本 */
.dark .win-lc-guide strong {
    color: #07c160;
}

/* 键盘按键样式 */
.win-lc-guide kbd {
    background-color: #f5f7fa;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 2px 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    color: #606266;
}

/* 黑色主题下的键盘按键样式 */
.dark .win-lc-guide kbd {
    background-color: #414243;
    border-color: #4c4d4f;
    color: #e4e7ed;
}



/* 图片区域水平居中 */
.image-section {
    text-align: center;
    margin: 16px 0;
}

/* 图片说明文字 */
.image-section .caption {
    color: #909399;
    font-size: 14px;
    margin-top: 8px;
    font-style: italic;
}

/* 黑色主题下的图片说明文字 */
.dark .image-section .caption {
    color: #a8abb2;
}

/* 分割线在黑色主题下的样式 */
.dark :deep(.el-divider--horizontal) {
    border-top-color: #4c4d4f;
}

/* 对话框在黑色主题下的背景 */
.dark :deep(.win-lc-guide-dialog .el-dialog) {
    background-color: #2b2b2b;
    border: 1px solid #4c4d4f;
}

/* 对话框标题在黑色主题下的样式 */
.dark :deep(.win-lc-guide-dialog .el-dialog__header) {
    background-color: #2b2b2b;
    border-bottom: 1px solid #4c4d4f;
}

.dark :deep(.win-lc-guide-dialog .el-dialog__title) {
    color: #ffffff;
}




::v-deep .el-dialog__header {
    font-weight: bolder;
}
.language-div{
    display:flex;
    align-items:center;
    width:100%;
    gap:10px;
}
.el-progress {
    width: 90px;
    margin: 0 auto;
}

.model-manager-dialog {
    display: flex;
    align-items: center;
    margin-top: 10vh !important;
}

:deep(.model-manager-dialog .el-dialog) {
    margin: 0 auto;
    height: 80vh;
    display: flex;
    flex-direction: column;
}

:deep(.model-manager-dialog .el-dialog__body) {
    flex: 1;
    overflow: auto;
    padding: 20px;
}
</style>
