/**
 * ASR数据处理工具类
 * 用于处理不同ASR引擎的识别结果，实现智能断句和去重
 */

// 导入在需要时会被使用

export default class AsrProcessor {
  constructor() {
    // Windows Live Caption 相关状态
    this.winLcSentText = '' // 已发送的完整句子文本
    this.winLcMinCharsAfterPeriod = 10 // 句号后至少等待的字符数

    // 对应C#中的状态
    this.displayOriginalCaption = '' // 对应C# Caption.DisplayOriginalCaption
    this.originalCaption = '' // 对应C# Caption.OriginalCaption

    // 新增：句子处理状态
    this.processedSentences = '' // 已经处理过的完整句子部分
    this.lastProcessedLength = 0 // 上次处理的文本长度

    // 常量定义，对应C# TextUtil
    this.PUNC_EOS = ['.', '?', '!', '。', '？', '！']
    this.SHORT_THRESHOLD = 10
    this.VERYLONG_THRESHOLD = 220
    this.STABLE_PUNCTUATION_THRESHOLD = 10 // 句号后字符数阈值
  }

  /**
   * 处理Windows Live Caption的ASR数据
   * 简化逻辑：按句号分割，句号前发送speechRecognized，剩余发送speechRecognizing
   * 句号后等待一定字符数再发送，避免过早断句
   * @param {Object} data - 包含caption字段的数据
   * @param {Function} handleAsrResult - 处理ASR结果的回调函数
   */
    /**
   * 处理Windows Live Caption的ASR数据
   * 重构版本：分离完整句子和进行中的文本
   */
  processWinLcAsrData(data, handleAsrResult) {
    console.log('win lc realtime data', data)

    if (!data.caption) return

    let fullText = data.caption
    console.log('原始文本:', fullText)

    if (!fullText) return

    // 预处理文本，对应C#中的预处理逻辑
    fullText = this.preprocessText(fullText)
    console.log('预处理后文本:', fullText)

    // 检查是否有新的完整句子
    const result = this.findStableSentences(fullText)

    if (result.hasNewSentence) {
      // 发现新的完整句子
      console.log('speechRecognized:', result.completeSentence)
      
      const completedData = {
        source: "win-lc",
        result: result.completeSentence,
        type: "speechRecognized"
      }
      handleAsrResult(null, { type: 'speechRecognized', data: JSON.stringify(completedData) })
      // 更新已处理的句子
      this.processedSentences = result.completeSentence

      // 如果有句子后的内容，也要输出
      if (result.remainingText.trim()) {
        console.log('speechRecognizing:', result.remainingText)
         const remainingData = {
          source: "win-lc",
          result: result.remainingText,
          type: "speechRecognizing"
        }
        handleAsrResult(null, { type: 'speechRecognizing', data: JSON.stringify(remainingData) })
      }
    } else {
      // 没有新句子，只输出当前进行中的文本
      if (result.remainingText.trim()) {
        const remainingData = {
          source: "win-lc",
          result: result.remainingText.trim(),
          type: "speechRecognizing"
        }
        handleAsrResult(null, { type: 'speechRecognizing', data: JSON.stringify(remainingData) })
      }
    }
  }

  /**
   * 预处理文本
   */
  preprocessText(text) {
    // 处理缩写：移除两个大写字母之间的点
    text = text.replace(/([A-Z])\s*\.\s*([A-Z])(?![A-Za-z]+)/g, '$1$2')
    // 如果缩写后跟单词，保留空格
    text = text.replace(/([A-Z])\s*\.\s*([A-Z])(?=[A-Za-z]+)/g, '$1 $2')
    // 标点符号周围的空格处理
    text = text.replace(/\s*([.!?,])\s*/g, '$1 ')
    // 中日文标点不需要空格
    text = text.replace(/\s*([。！？，、])\s*/g, '$1')

    return text
  }
  
  normalizeEndingPunctuation(text) {
    return text.replace(/[。,.，]*$/, '')
  }


  /**
   * 查找稳定的句子
   * @param {string} fullText - 完整文本
   * @returns {Object} 包含句子信息的对象
   */
  findStableSentences(fullText) {
    // 如果文本比上次处理的还短，说明可能是新的会话，重置状态
    // if (fullText.length < this.lastProcessedLength) {
    //   console.log('文本长度减少，重置处理状态')
    //   this.processedSentences = ''
    //   this.lastProcessedLength = 0
    // }

    // 检查是否有新内容
    // if (fullText.length <= this.processedSentences.length) {
    //   return {
    //     hasNewSentence: false,
    //     completeSentence: '',
    //     remainingText: '',
    //     allProcessedText: this.processedSentences
    //   }
    // }

    // 从未处理的部分开始查找所有稳定的句子
    // let unprocessedText = fullText.substring(this.processedSentences.length)
    let unprocessedText = ''
    const index = fullText.indexOf(this.processedSentences)
    if (index >= 0) {
      // 截取 b 后面的内容
      const afterText = fullText.substring(index + this.processedSentences.length).trim()
      unprocessedText = afterText
    } else {
        const index = fullText.indexOf(this.processedSentences.substring(0,this.processedSentences.length - 1))
        const afterText = fullText.substring(index + this.processedSentences.length).trim()
        unprocessedText = afterText
    }
    console.log('findStableSentences 已处理文本:', this.processedSentences)
    console.log('findStableSentences 未处理文本:', `"${unprocessedText}"`)

    // 从后往前查找最后一个稳定的句号
    const lastStableEnd = this.findLastStableSentenceEnd(unprocessedText, this.processedSentences.length, fullText)

    if (lastStableEnd > 0) {
      // 找到稳定的句号，只输出最后一个稳定的句子
      const lastSentenceInfo = this.extractLastStableSentence(unprocessedText, lastStableEnd)
      // 完整句子
      const completeSentence = lastSentenceInfo.sentence
      const remainingText = unprocessedText.substring(lastStableEnd).trim(); // 稳定句号后剩余的文本

      this.lastProcessedLength = fullText.length

      return {
        hasNewSentence: true,
        completeSentence,
        remainingText
      }
    } else {
      // 没有找到稳定的句号
      this.lastProcessedLength = fullText.length

      return {
        hasNewSentence: false,
        completeSentence: '',
        remainingText: unprocessedText.trim()
      }
    }
  }

  /**
   * 从后往前查找最后一个稳定句号的结束位置
   * @param {string} unprocessedText - 未处理的文本
   * @param {number} processedLength - 已处理的文本长度
   * @param {string} fullText - 完整文本（用于检查稳定性）
   * @returns {number} 最后一个稳定句号的结束位置（在未处理文本中），0表示没有找到
   */
  findLastStableSentenceEnd(unprocessedText) {
    const eosPositions = []

    for (let i = 0; i < unprocessedText.length; i++) {
      if (this.isPuncEOS(unprocessedText[i])) {
        eosPositions.push(i)
      }
    }

    console.log(`找到 ${eosPositions.length} 个句号位置:`, eosPositions)

    for (let i = eosPositions.length - 1; i >= 0; i--) {
      const eosPos = eosPositions[i]
      const charsAfter = unprocessedText.length - (eosPos + 1)

      console.log(`检查句号位置 ${eosPos}, 后面内容长度: ${charsAfter}`)

      if (charsAfter >= this.STABLE_PUNCTUATION_THRESHOLD) {
        console.log(`找到最后一个稳定句号位置: ${eosPos}`)
        return eosPos + 1  // 句号后一个字符索引
      }
    }

    console.log('未找到稳定的句号')
    return 0
  }


  /**
   * 提取最后一个稳定的句子
   * @param {string} unprocessedText - 未处理的文本
   * @param {number} lastStableEnd - 最后一个稳定句号的结束位置
   * @returns {Object} 包含句子信息的对象
   */
  extractLastStableSentence(unprocessedText, lastStableEnd) {
    // 找到最后一个稳定句号之前的所有句号位置
    const eosPositions = []

    for (let i = 0; i < lastStableEnd - 1; i++) { // -1 因为 lastStableEnd 是句号后的位置
      if (this.isPuncEOS(unprocessedText[i])) {
        eosPositions.push(i)
      }
    }

    let sentenceStart = 0
    if (eosPositions.length > 0) {
      // 如果有前面的句号，从最后一个句号后开始
      sentenceStart = eosPositions[eosPositions.length - 1] + 1
    }

    // 提取最后一个句子
    const sentence = unprocessedText.substring(0, lastStableEnd).trim()

    console.log(`提取最后一个稳定句子: "${sentence}" (位置 0-${lastStableEnd})`)

    return {
      sentence,
      startPosition: sentenceStart,
      endPosition: lastStableEnd
    }
  }

  /**
   * 按句号分割文本，并判断句号后字符数是否达到要求
   * @param {string} text
   * @param {number} minCharsAfterPeriod
   * @returns {Object}
   */
  splitBySentenceEnd(text, minCharsAfterPeriod = 10) {
    const sentenceEndRegex = /[.。]/g
    let lastValidPeriodIndex = -1
    let match

    while ((match = sentenceEndRegex.exec(text)) !== null) {
      const periodIndex = match.index
      const charsAfter = text.length - (periodIndex + 1)
      if (charsAfter >= minCharsAfterPeriod) {
        lastValidPeriodIndex = periodIndex
      }
    }

    if (lastValidPeriodIndex >= 0) {
      return {
        beforePeriod: text.slice(0, lastValidPeriodIndex + 1).trim(),
        afterPeriod: text.slice(lastValidPeriodIndex + 1).trim(),
        hasNewPeriod: true
      }
    }

    return {
      beforePeriod: '',
      afterPeriod: text.trim(),
      hasNewPeriod: false
    }
  }


  /**
   * 判断是否应该更新显示内容
   * 综合去重逻辑，避免重复显示相似内容
   */
  shouldUpdateDisplay(newCaption) {
    const oldText = this.displayOriginalCaption.trim()
    const newText = newCaption.trim()

    // 如果完全相同，不更新
    if (oldText === newText) {
      console.log('跳过更新：文本完全相同')
      return false
    }

    // 如果新内容为空，不更新
    if (!newText) {
      console.log('跳过更新：新文本为空')
      return false
    }

    // 如果当前显示内容为空，直接更新
    if (!oldText) {
      console.log('首次更新：当前显示为空')
      return true
    }

    // 移除末尾标点符号进行比较
    const oldTextNoPunc = oldText.replace(/[.。?!？！]\s*$/, '').trim()
    const newTextNoPunc = newText.replace(/[.。?!？！]\s*$/, '').trim()

    // 情况1：新文本是旧文本的延续（前缀匹配）
    // 例如：旧文本 "But then I." -> 新文本 "But then I post my"
    if (newTextNoPunc.startsWith(oldTextNoPunc) && newTextNoPunc.length > oldTextNoPunc.length) {
      // 检查延续部分的长度，如果延续部分太短，可能是同一句话
      const extensionLength = newTextNoPunc.length - oldTextNoPunc.length
      if (extensionLength < 15) { // 延续部分少于15个字符，认为是同一句话
        console.log('跳过重复：新文本是旧文本的短延续', {
          old: oldText,
          new: newText,
          extensionLength
        })
        return false
      }
    }

    // 情况2：旧文本是新文本的延续（可能是回退）
    if (oldTextNoPunc.startsWith(newTextNoPunc) && oldTextNoPunc.length > newTextNoPunc.length) {
      console.log('跳过重复：旧文本是新文本的延续（可能是回退）', {
        old: oldText,
        new: newText
      })
      return false
    }

    // 情况3：检查相似度，避免微小变化的重复
    const similarity = this.calculateSimilarity(oldTextNoPunc, newTextNoPunc)
    if (similarity > 0.8) { // 相似度超过80%
      console.log('跳过重复：文本相似度过高', {
        old: oldText,
        new: newText,
        similarity: similarity.toFixed(2)
      })
      return false
    }

    console.log('允许更新：文本有显著变化', { old: oldText, new: newText })
    return true
  }

  /**
   * 计算两个字符串的相似度（简单的Jaccard相似度）
   */
  calculateSimilarity(str1, str2) {
    if (!str1 || !str2) return 0

    const words1 = new Set(str1.toLowerCase().split(/\s+/))
    const words2 = new Set(str2.toLowerCase().split(/\s+/))

    const intersection = new Set([...words1].filter(x => words2.has(x)))
    const union = new Set([...words1, ...words2])

    return intersection.size / union.size
  }

  /**
   * 判断字符是否为句子结束标点
   */
  isPuncEOS(char) {
    return this.PUNC_EOS.includes(char)
  }

  /**
   * 查找字符串中任意字符的最后出现位置
   */
  lastIndexOfAny(str, chars) {
    let lastIndex = -1
    for (let char of chars) {
      const index = str.lastIndexOf(char)
      if (index > lastIndex) {
        lastIndex = index
      }
    }
    return lastIndex
  }

  /**
   * 获取字符串的UTF-8字节长度
   */
  getByteLength(str) {
    return new TextEncoder().encode(str).length
  }

  /**
   * 判断字符是否为中日韩字符
   */
  isCJChar(char) {
    const code = char.charCodeAt(0)
    return (code >= 0x4E00 && code <= 0x9FFF) ||  // CJK统一汉字
           (code >= 0x3400 && code <= 0x4DBF) ||  // CJK扩展A
           (code >= 0x3040 && code <= 0x30FF)     // 平假名和片假名
  }

  /**
   * 替换换行符，对应C# TextUtil.ReplaceNewlines
   */
  replaceNewlines(text, byteThreshold) {
    const splits = text.split('\n')
    for (let i = 0; i < splits.length; i++) {
      splits[i] = splits[i].trim()
      if (i === splits.length - 1) continue

      const lastChar = splits[i][splits[i].length - 1]
      if (this.getByteLength(splits[i]) >= byteThreshold) {
        splits[i] += this.isCJChar(lastChar) ? '。' : '. '
      } else {
        splits[i] += this.isCJChar(lastChar) ? '——' : '—'
      }
    }
    return splits.join('')
  }

  /**
   * 缩短显示句子，对应C# TextUtil.ShortenDisplaySentence
   */
  shortenDisplaySentence(text, maxByteLength) {
    const PUNC_COMMA = [',', '，', '、', '—', '\n']
    const allPunc = [...this.PUNC_EOS, ...PUNC_COMMA]

    while (this.getByteLength(text) >= maxByteLength) {
      let puncIndex = -1
      for (let char of allPunc) {
        const index = text.indexOf(char)
        if (index >= 0 && (puncIndex === -1 || index < puncIndex)) {
          puncIndex = index
        }
      }
      if (puncIndex < 0 || puncIndex + 1 >= text.length) break
      text = text.substring(puncIndex + 1)
    }
    return text
  }

  /**
   * 重置Windows Live Caption的状态
   * 在开始新的识别会话时调用
   */
  resetWinLcState() {
    this.displayOriginalCaption = ''
    this.processedSentences = ''
    this.lastProcessedLength = 0
    console.log('重置ASR处理状态')
  }

  /**
   * 获取当前Windows Live Caption的状态
   * @returns {Object} 当前状态
   */
  getWinLcState() {
    return {
      displayOriginalCaption: this.displayOriginalCaption,
      processedSentences: this.processedSentences,
      lastProcessedLength: this.lastProcessedLength
    }
  }
}
