import CryptoJS from 'crypto-js';
import {getAsrWssAdress} from "@/api/AsrApi";
class WebSocketClient {
    constructor() {
        this.ws = null;
        this.url = '';
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 3000; // 3秒重连间隔
        this.heartbeatInterval = null;
        this.eventListeners = new Map();
    }

    async getWebSocketUrl(lang) {
        //新的获取wss方式
        try {
            const response = await getAsrWssAdress()
            console.log('response ',response)
            if(response && response.url) {
                return response.url
            } else {
                console.warn('Failed to get WebSocket URL from API, using fallback method')
            }
        } catch (error) {
            console.error('Error getting WebSocket URL from API:', error)
            console.log('Using fallback WebSocket URL generation method')
        }

        // 请求地址根据语种不同变化
        var url = "wss://rtasr.xfyun.cn/v1/ws";
        var appId = '5cb942c1';
        var secretKey = 'cc89c37ef6ecae76be24e0dec8b9818b';
        var ts = Math.floor(new Date().getTime() / 1000);
        var signa = CryptoJS.MD5(appId + ts);
        signa = signa.toString(CryptoJS.enc.Hex)
        var signatureSha = CryptoJS.HmacSHA1(signa, secretKey);
        var signature = CryptoJS.enc.Base64.stringify(signatureSha);
        signature = encodeURIComponent(signature)
        let address = `${url}?appid=${appId}&ts=${ts}&signa=${signature}`;
        if(lang){
            address += '&lang=' + lang
        }
        return address
    }

    // 连接方法
    async connect(lang) {
        this.url = await this.getWebSocketUrl(lang);
        console.log('xf asr : ',this.url)
        return new Promise((resolve, reject) => {
            try {
                this.ws = new WebSocket(this.url);
                
                // 连接成功
                this.ws.onopen = () => {
                    console.log('WebSocket connected');
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    // this.startHeartbeat();
                    this.emit('connected', { status: 'connected' });
                    resolve(true);
                };

                // 接收消息
                this.ws.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        this.emit('message', data);
                        
                        // 根据消息类型触发不同事件
                        if (data.type) {
                            this.emit(data.type, data);
                        }
                    } catch (error) {
                        console.error('Failed to parse message:', error);
                        this.emit('error', { error: 'Message parse error' });
                    }
                };

                // 连接关闭
                this.ws.onclose = () => {
                    console.log('WebSocket closed');
                    this.isConnected = false;
                    this.stopHeartbeat();
                    this.emit('disconnected', { status: 'disconnected' });
                    // this.tryReconnect();
                };

                // 错误处理
                this.ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    this.emit('error', { error: 'Connection error' });
                    reject(error);
                };

            } catch (error) {
                console.error('Failed to create WebSocket:', error);
                reject(error);
            }
        });
    }

    // 重连机制
    tryReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('Max reconnection attempts reached');
            this.emit('reconnect_failed', { attempts: this.reconnectAttempts });
            return;
        }

        this.reconnectAttempts++;
        console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.emit('reconnecting', { attempt: this.reconnectAttempts });

        setTimeout(() => {
            this.connect(this.url).catch(() => {
                this.tryReconnect();
            });
        }, this.reconnectInterval);
    }

    // 心跳机制
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.send({ type: 'heartbeat', timestamp: Date.now() });
            }
        }, 30000); // 30秒发送一次心跳
    }

    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    // 发送消息
    send(data) {
        if (!this.isConnected) {
            throw new Error('WebSocket is not connected');
        }
        
        const message = typeof data === 'string' ? data : JSON.stringify(data);
        this.ws.send(message);
    }

    sendBinary(data) {
        if (!this.isConnected) {
            throw new Error('WebSocket is not connected');
        }
        
        this.ws.send(data);
    }

    // 事件监听器管理
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    off(event, callback) {
        if (!this.eventListeners.has(event)) return;
        
        const callbacks = this.eventListeners.get(event);
        const index = callbacks.indexOf(callback);
        if (index !== -1) {
            callbacks.splice(index, 1);
        }
    }

    emit(event, data) {
        if (!this.eventListeners.has(event)) return;
        
        this.eventListeners.get(event).forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error(`Error in event listener for ${event}:`, error);
            }
        });
    }

    // 关闭连接
    disconnect() {
        return new Promise((resolve) => {
            if (this.ws) {
                this.ws.close();
                resolve();
            } else {
                resolve();
            }
        });
    }
}

// 创建单例实例
const xunfeiClient = new WebSocketClient();

export default xunfeiClient;
