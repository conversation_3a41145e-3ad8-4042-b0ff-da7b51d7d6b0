/* eslint-env node */

/*
 * This file runs in a Node context (it's NOT transpiled by Babel), so use only
 * the ES6 features that are supported by your Node version. https://node.green/
 */

// Configuration for your app
// https://v2.quasar.dev/quasar-cli-vite/quasar-config-js


const { configure } = require('quasar/wrappers');


module.exports = configure(function (/* ctx */) {
  return {
    // https://v2.quasar.dev/quasar-cli-vite/prefetch-feature
    // preFetch: true,

    // app boot file (/src/boot)
    // --> boot files are part of "main.js"
    // https://v2.quasar.dev/quasar-cli-vite/boot-files
    boot: [
      
      'axios',
      'element-plus'
    ],

    // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#css
    css: [
      'app.scss'
    ],

    // https://github.com/quasarframework/quasar/tree/dev/extras
    extras: [
      'ionicons-v4',
      // 'mdi-v7',
      // 'fontawesome-v6',
      // 'eva-icons',
      // 'themify',
      // 'line-awesome',
      // 'roboto-font-latin-ext', // this or either 'roboto-font', NEVER both!

      'roboto-font', // optional, you are not bound to it
      'material-icons', // optional, you are not bound to it
    ],

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#build
    build: {
      target: {
        browser: [ 'es2019', 'edge88', 'firefox78', 'chrome87', 'safari13.1' ],
        node: 'node20'
      },

      vueRouterMode: 'hash', // available values: 'hash', 'history'
      // vueRouterBase,
      // vueDevtools,
      // vueOptionsAPI: false,

      // rebuildCache: true, // rebuilds Vite/linter/etc cache on startup

      // publicPath: '/',
      // analyze: true,
      // env: {},
      // rawDefine: {}
      // ignorePublicFolder: true,
      // minify: false,
      // polyfillModulePreload: true,
      // distDir

      // extendViteConf (viteConf) {},
      // viteVuePluginOptions: {},

      
      // vitePlugins: [
      //   [ 'package-name', { ..pluginOptions.. }, { server: true, client: true } ]
      // ]
      alias: {'@': '/src'}
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#devServer
    devServer: {
      // https: true
      port: 8080,
      open: true, // opens browser window automatically
      proxy: {
        '/conf': {
          target: 'http://117.72.99.27:8128/conf/',
          // target: 'https://boothapi.gtmeeting.com/conf/',
          changeOrigin: true, 
          rewrite: (p) => p.replace(/^\/conf/, '')
        },
        '/microsoftTranslate': {
          target: 'https://api.cognitive.microsofttranslator.com/translate',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/microsoftTranslate/, '')
        },
        '/googleTranslate': {
          target: 'https://clients5.google.com/translate_a/t',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/googleTranslate/, '')
        },
        '/chromeExtensionTranslate': {
          target: 'https://dictionaryextension-pa.googleapis.com/v1/dictionaryExtensionData',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/chromeExtensionTranslate/, '')
        },
        '/xfyunAsr': {
          target: 'http://117.72.99.27:15005/getAsrToken/api',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/xfyunAsr/, '')
        },
        '/azureAsrToken': {
          target: 'http://117.72.99.27:15005/getAzureToken/api',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/azureAsrToken/, '')
        },
        '/translate': {
          target: 'http://117.72.99.27:1188/translate',
          // target: 'https://deeplx.chinarui.cn/translate',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/translate/, '')
        },
        '/convert': {
          target: 'http://127.0.0.1:50055/convert',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/convert/, '')
        },
        '/punctuator': {
          target: 'http://bark.phon.ioc.ee/punctuator',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/punctuator/, '')
        }
      }
    },

    // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#framework
    framework: {
      config: {},

      // iconSet: 'material-icons', // Quasar icon set
      // lang: 'en-US', // Quasar language pack

      // For special cases outside of where the auto-import strategy can have an impact
      // (like functional components as one of the examples),
      // you can manually specify Quasar components/directives to be available everywhere:
      //
      // components: [],
      // directives: [],

      // Quasar plugins
      plugins: []
    },

    // animations: 'all', // --- includes all animations
    // https://v2.quasar.dev/options/animations
    animations: [],

    // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#property-sourcefiles
    // sourceFiles: {
    //   rootComponent: 'src/App.vue',
    //   router: 'src/router/index',
    //   store: 'src/store/index',
    //   registerServiceWorker: 'src-pwa/register-service-worker',
    //   serviceWorker: 'src-pwa/custom-service-worker',
    //   pwaManifestFile: 'src-pwa/manifest.json',
    //   electronMain: 'src-electron/electron-main',
    //   electronPreload: 'src-electron/electron-preload'
    // },

    // https://v2.quasar.dev/quasar-cli-vite/developing-ssr/configuring-ssr
    ssr: {
      // ssrPwaHtmlFilename: 'offline.html', // do NOT use index.html as name!
                                          // will mess up SSR

      // extendSSRWebserverConf (esbuildConf) {},
      // extendPackageJson (json) {},

      pwa: false,

      // manualStoreHydration: true,
      // manualPostHydrationTrigger: true,

      prodPort: 3000, // The default port that the production server should use
                      // (gets superseded if process.env.PORT is specified at runtime)

      middlewares: [
        'render' // keep this as last one
      ]
    },

    // https://v2.quasar.dev/quasar-cli-vite/developing-pwa/configuring-pwa
    pwa: {
      workboxMode: 'generateSW', // or 'injectManifest'
      injectPwaMetaTags: true,
      swFilename: 'sw.js',
      manifestFilename: 'manifest.json',
      useCredentialsForManifestTag: false,
      // useFilenameHashes: true,
      // extendGenerateSWOptions (cfg) {}
      // extendInjectManifestOptions (cfg) {},
      // extendManifestJson (json) {}
      // extendPWACustomSWConf (esbuildConf) {}
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-cordova-apps/configuring-cordova
    cordova: {
      // noIosLegacyBuildFlag: true, // uncomment only if you know what you are doing
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-capacitor-apps/configuring-capacitor
    capacitor: {
      hideSplashscreen: true
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-electron-apps/configuring-electron
    electron: {
      // extendElectronMainConf (esbuildConf)
      // extendElectronPreloadConf (esbuildConf)

      // specify the debugging port to use for the Electron app when running in development mode
      inspectPort: 5859,

      bundler: 'builder', // 'packager' or 'builder'

      packager: {
        // https://github.com/electron-userland/electron-packager/blob/master/docs/api.md#options

        // OS X / Mac App Store
        // appBundleId: '',
        // appCategoryType: '',
        // osxSign: '',
        // protocol: 'myapp://path',

        // Windows only
        // win32metadata: { ... }
      },

      builder: {
        // https://www.electron.build/configuration/configuration

        appId: 'gt-interpretease',
        "publish": [
          {
            "provider": "generic",
            "url": "http://oss.chinarui.cn/greenterp/terpmeta/"
          }
        ],
        afterSign:'notarize.js',
        asar:false,
        buildDependenciesFromSource:true,
        "extraResources": [{
          "from": "./lib/gtasr",
          "to": "../lib/gtasr"
        },{
          "from": "./lib/GreenTerpAudio-2.0.pkg",
          "to": "../lib/GreenTerpAudio-2.0.pkg"
        },{
          "from": "./lib/osx-audio-input",
          "to": "../lib/osx-audio-input"
        },{
          "from": "./lib/pb-mac",
          "to": "../lib/pb-mac"
        },{
          "from": "./lib/pb-win",
          "to": "../lib/pb-win"
        },{
          "from": "./lib/pdf2word",
          "to": "../lib/pdf2word"
        },
        {
          "from": "./lib/GTEAudio.pkg",
          "to": "../lib/GTEAudio.pkg"
        },{
          "from": "./lib/GetNir.exe",
          "to": "../lib/GetNir.exe"
        },{
          "from": "./lib/SoundVolumeView.exe",
          "to": "../lib/SoundVolumeView.exe"
        },{
          "from": "./lib/gt_offline_asr",
          "to": "../lib/gt_offline_asr"
        },{
          "from": "./lib/gt_offline_asr_intel",
          "to": "../lib/gt_offline_asr_intel"
        },{
          "from": "./lib/GTEController.dmg",
          "to": "../lib/GTEController.dmg"
        },{
          "from": "./lib/GTEAudio",
          "to": "../lib/GTEAudio"
        },{
          "from": "./lib/gt_offline_asr_win",
          "to": "../lib/gt_offline_asr_win"
        },{
          "from": "./lib/detect_lang",
          "to": "../lib/detect_lang"
        },{
          "from": "./lib/detect_lang_win",
          "to": "../lib/detect_lang_win"
        },{
          "from": "./lib/detect_lang_intel",
          "to": "../lib/detect_lang_intel"
        },{
          "from": "./lib/GTEControllerSetup.exe",
          "to": "../lib/GTEControllerSetup.exe"
        },{
          "from": "./lib/win_offline_asr", //windows live caption
          "to": "../lib/win_offline_asr"
        }],
        "mac":{
          "entitlements": "entitlements.mac.plist",
          "entitlementsInherit":"entitlements.mac.plist",
          "extendInfo": {
            "NSSpeechRecognitionUsageDescription": "Your app uses speech recognition"
          },
          "target": [
            {
              "target": "default",
              "arch": ["x64"]
            }
          ]
        },
      }
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-browser-extensions/configuring-bex
    bex: {
      contentScripts: [
        'my-content-script'
      ],

      // extendBexScriptsConf (esbuildConf) {}
      // extendBexManifestJson (json) {}
    }
  }
});
